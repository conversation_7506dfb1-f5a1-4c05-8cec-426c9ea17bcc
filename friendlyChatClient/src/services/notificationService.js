import notifee, { EventType } from '@notifee/react-native';
import { AppState } from 'react-native';
import SocketService from './socketIoService';

export async function notifyUser(text, ignoreInForeground, partnerInfo = {}) {
  // Check if app is in foreground and should ignore notification
  if (ignoreInForeground && AppState.currentState === 'active') {
    return;
  }

  // Create notification with reply action
  await notifee.displayNotification({
    id: `message-${Date.now()}`,
    title: partnerInfo.name ? `${partnerInfo.name}:` : 'Stranger:',
    body: text,
    data: {
      type: 'message',
      partnerId: partnerInfo._id || 'unknown',
      sessionId: partnerInfo.sessionId || 'unknown',
      timestamp: Date.now().toString()
    },
    android: {
      channelId: 'default-channel-id',
      smallIcon: 'ic_notification',
      largeIcon: 'ic_notification',
      sound: 'default',
      pressAction: {
        id: 'default',
      },
      // Add reply action for Android
      actions: [
        {
          title: 'Reply',
          pressAction: {
            id: 'reply',
          },
          input: {
            placeholder: 'Type a reply...',
            allowFreeFormInput: true,
          },
        },
      ],
    },
    ios: {
      sound: 'default',
      // Add reply action for iOS
      categoryId: 'MESSAGE_REPLY',
    },
  });
}

// Handle notification events (including replies)
export async function setupNotificationHandlers(addMessageCallback) {
  // Listen for notification press events

  // Listen for background events (including reply actions)
  notifee.onBackgroundEvent(async ({ type, detail }) => {
    console.log('Background event:', type, detail);
    if (type === EventType.ACTION_PRESS && detail.pressAction.id === 'reply') {
      console.log('Reply received from notification:', detail.input);

      if (detail.input) {
        const replyText = detail.input;
        const partnerData = detail.notification?.data;

        // Send the reply message
        if (partnerData && partnerData.sessionId) {
          const message = {
            text: replyText,
            timestamp: Date.now(),
            sessionId: partnerData.sessionId
          };

          try {
            // Add message to local state first (if callback provided)
            if (addMessageCallback) {
              addMessageCallback({
                text: replyText,
                timestamp: Date.now(),
                source: 'user',
                status: 'pending'
              });
            }

            SocketService.chat(message);
            console.log('Message sent from notification reply:', replyText);

            // Show confirmation notification
            await notifee.displayNotification({
              id: `reply-sent-${Date.now()}`,
              title: 'Reply Sent',
              body: `"${replyText}"`,
              android: {
                channelId: 'reply-channel-id',
                smallIcon: 'ic_notification',
                autoCancel: true,
              },
              ios: {
                sound: null,
              },
            });

          } catch (error) {
            console.error('Failed to send reply from notification:', error);
          }
        }
      }
    }
  });
}

// Send a quick reply from notification
export async function sendQuickReply(replyText, partnerInfo, addMessageCallback) {
  if (!replyText || !partnerInfo || !partnerInfo.sessionId) {
    console.error('Missing required parameters for quick reply');
    return false;
  }

  try {
    const message = {
      text: replyText,
      timestamp: Date.now(),
      sessionId: partnerInfo.sessionId
    };

    // Add message to local state first (if callback provided)
    if (addMessageCallback) {
      addMessageCallback({
        text: replyText,
        timestamp: Date.now(),
        source: 'user',
        status: 'pending'
      });
    }

    SocketService.chat(message);
    console.log('Quick reply sent:', replyText);
    return true;
  } catch (error) {
    console.error('Failed to send quick reply:', error);
    return false;
  }
}
