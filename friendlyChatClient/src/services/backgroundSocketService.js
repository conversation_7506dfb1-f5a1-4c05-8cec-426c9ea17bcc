import notifee, { EventType } from '@notifee/react-native';
import { AppState, Platform } from 'react-native';
import SocketService, { isSocketConnected } from './socketIoService';

class BackgroundSocketService {
    constructor() {
        this.isForegroundServiceRunning = false;
        this.appState = AppState.currentState;
        this.setupAppStateListener();
    }

    setupAppStateListener() {
        AppState.addEventListener('change', (nextAppState) => {
            console.log('App state changed:', this.appState, '->', nextAppState);

            if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
                // App came to foreground - stop foreground service
                this.stopForegroundService();
            } else if (this.appState === 'active' && nextAppState.match(/inactive|background/)) {
                // App going to background - start foreground service to maintain connection
                this.startForegroundService();
            }
            this.appState = nextAppState;
        });
    }

    async startForegroundService() {
        if (this.isForegroundServiceRunning) return;

        try {
            // Create a channel (required for Android)
            const channelId = await notifee.createChannel({
                id: 'socket-service',
                name: 'Socket Connection Service',
                importance: Platform.OS === 'android' ? 2 : 0, // IMPORTANCE_LOW for Android
            });

            // Start foreground service
            await notifee.displayNotification({
                id: 'socket-service',
                title: 'Maintaining chat connection',
                body: 'Your chat session is being maintained in background',
                android: {
                    channelId,
                    asForegroundService: true,
                    ongoing: true,
                    autoCancel: false,
                    importance: 2, // IMPORTANCE_LOW
                    smallIcon: 'ic_notification',
                    largeIcon: 'ic_notification',
                    pressAction: {
                        id: 'default',
                    },
                },
                ios: {
                    // iOS doesn't support foreground services in the same way
                    // but we can still show a notification
                    foregroundPresentationOptions: {
                        alert: true,
                        badge: true,
                        sound: true,
                    },
                },
            });

            this.isForegroundServiceRunning = true;
            console.log('Foreground service started for socket maintenance');

        } catch (error) {
            console.error('Failed to start foreground service:', error);
        }
    }

    async stopForegroundService() {
        if (!this.isForegroundServiceRunning) return;

        try {
            await notifee.cancelNotification('socket-service');
            this.isForegroundServiceRunning = false;
        } catch (error) {
            console.error('Failed to stop foreground service:', error);
        }
    }

    // Method to manually start/stop the service if needed
    async enableBackgroundConnection(enable = true) {
        if (enable) {
            await this.startForegroundService();
        } else {
            await this.stopForegroundService();
        }
    }
}

// Export singleton instance
export default new BackgroundSocketService();